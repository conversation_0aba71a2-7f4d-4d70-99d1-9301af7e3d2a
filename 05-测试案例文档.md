# 真淘社交平台测试案例文档

## 1. 测试概述

### 1.1 测试目标
确保真淘社交平台的各项功能正常运行，用户体验良好，系统稳定可靠。

### 1.2 测试范围
- 功能测试
- 接口测试
- 性能测试
- 安全测试
- 兼容性测试

### 1.3 测试环境
- **操作系统**: Windows 10, macOS, iOS, Android
- **浏览器**: Chrome, Safari, Firefox
- **微信版本**: 最新版本
- **测试设备**: iPhone, Android手机, 电脑

## 2. 功能测试案例

### 2.1 用户登录模块

#### 测试案例 TC001: 微信登录成功
**测试目的**: 验证用户能够通过微信成功登录

**前置条件**: 
- 用户已安装微信
- 用户有微信账号

**测试步骤**:
1. 打开"爱聊"小程序
2. 点击"微信登录"按钮
3. 在微信授权页面点击"允许"
4. 观察登录结果

**预期结果**:
- 成功跳转到首页
- 显示用户微信头像和昵称
- 本地存储登录状态

**测试数据**: 正常微信账号

---

#### 测试案例 TC002: 微信登录取消授权
**测试目的**: 验证用户取消微信授权时的处理

**测试步骤**:
1. 打开"爱聊"小程序
2. 点击"微信登录"按钮
3. 在微信授权页面点击"拒绝"
4. 观察处理结果

**预期结果**:
- 显示登录失败提示
- 返回登录页面
- 不保存任何用户信息

### 2.2 实名认证模块

#### 测试案例 TC003: 实名认证成功
**测试目的**: 验证用户能够成功完成实名认证

**前置条件**: 用户已登录

**测试步骤**:
1. 进入"我的"页面
2. 点击"实名认证"
3. 输入真实姓名"张三"
4. 输入身份证号"110101199001011234"
5. 点击"提交认证"

**预期结果**:
- 显示"认证成功"提示
- 用户资料显示已认证标识
- 可以使用需要认证的功能

**测试数据**: 
- 姓名: 张三
- 身份证: 110101199001011234

---

#### 测试案例 TC004: 实名认证信息错误
**测试目的**: 验证输入错误信息时的处理

**测试步骤**:
1. 进入实名认证页面
2. 输入姓名"张三"
3. 输入错误身份证号"123456"
4. 点击"提交认证"

**预期结果**:
- 显示"身份证号格式错误"提示
- 认证失败，不保存信息
- 用户可以重新输入

### 2.3 交友圈功能模块

#### 测试案例 TC005: 发布交友圈成功
**测试目的**: 验证用户能够成功发布交友圈

**前置条件**: 用户已登录

**测试步骤**:
1. 进入"交友圈"页面
2. 点击"+"发布按钮
3. 输入标题"今天天气真好"
4. 输入内容"阳光明媚，心情愉快"
5. 选择一张图片
6. 点击"发布"

**预期结果**:
- 显示"发布成功"提示
- 交友圈出现在交友圈列表
- 包含标题、内容、图片和发布时间

**测试数据**:
- 标题: 今天天气真好
- 内容: 阳光明媚，心情愉快
- 图片: test.jpg

---

#### 测试案例 TC006: 交友圈点赞功能
**测试目的**: 验证交友圈点赞功能正常

**前置条件**: 
- 用户已登录
- 存在可点赞的博客

**测试步骤**:
1. 进入交友圈详情页
2. 点击"❤️"点赞按钮
3. 观察点赞状态变化
4. 再次点击取消点赞

**预期结果**:
- 点赞后按钮变为红色
- 点赞数量+1
- 取消点赞后按钮变为灰色
- 点赞数量-1

---

#### 测试案例 TC007: 交友圈评论功能
**测试目的**: 验证交友圈评论功能正常

**测试步骤**:
1. 进入交友圈详情页
2. 点击评论输入框
3. 输入评论"写得很好！"
4. 点击"发送"

**预期结果**:
- 评论成功发布
- 评论显示在评论列表
- 评论数量+1

### 2.4 附近的人模块

#### 测试案例 TC008: 查看附近的人
**测试目的**: 验证能够正常查看附近的用户

**前置条件**: 
- 用户已登录
- 已授权位置信息

**测试步骤**:
1. 点击"附近的人"标签
2. 授权位置信息
3. 查看用户列表

**预期结果**:
- 显示附近用户列表
- 显示用户头像、昵称、距离
- 可以点击查看用户详情

## 3. 接口测试案例

### 3.1 用户登录接口

#### 测试案例 API001: 登录接口正常调用
**接口**: POST /api/user/login

**测试数据**:
```json
{
    "code": "valid_wx_code",
    "userInfo": {
        "nickName": "测试用户",
        "avatarUrl": "https://example.com/avatar.jpg"
    }
}
```

**预期响应**:
```json
{
    "code": 0,
    "msg": "登录成功",
    "data": {
        "token": "jwt_token_string",
        "userInfo": {
            "id": "user_id",
            "username": "测试用户"
        }
    }
}
```

---

#### 测试案例 API002: 登录接口参数缺失
**测试数据**:
```json
{
    "code": ""
}
```

**预期响应**:
```json
{
    "code": 9001,
    "msg": "参数错误",
    "data": null
}
```

### 3.2 博客接口测试

#### 测试案例 API003: 获取交友圈列表
**接口**: GET /blog/list?page=1&size=10

**预期响应**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "title": "博客标题",
                "content": "博客内容",
                "likedCount": 15,
                "createTime": "2024-01-01 12:00:00"
            }
        ]
    }
}
```

#### 测试案例 API004: 交友圈点赞接口
**接口**: GET /blog/likes/1?userId=user123

**预期响应**:
```json
{
    "code": 0,
    "msg": "点赞成功",
    "data": {
        "isLiked": true,
        "likedCount": 16
    }
}
```

## 4. 性能测试案例

### 4.1 并发测试

#### 测试案例 PERF001: 登录接口并发测试
**测试目的**: 验证登录接口在高并发下的性能

**测试条件**:
- 并发用户数: 100
- 测试时间: 5分钟
- 期望响应时间: <2秒

**测试步骤**:
1. 使用JMeter创建100个并发线程
2. 每个线程循环调用登录接口
3. 记录响应时间和成功率

**通过标准**:
- 平均响应时间 < 2秒
- 成功率 > 95%
- 无系统错误

---

#### 测试案例 PERF002: 交友圈列表加载性能
**测试目的**: 验证交友圈列表在大数据量下的加载性能

**测试条件**:
- 数据量: 10万条博客
- 分页大小: 20条/页
- 期望响应时间: <1秒

**通过标准**:
- 首页加载时间 < 1秒
- 翻页响应时间 < 0.5秒
- 内存使用稳定

### 4.2 压力测试

#### 测试案例 STRESS001: 系统极限压力测试
**测试目的**: 找出系统的性能瓶颈

**测试步骤**:
1. 逐步增加并发用户数
2. 监控系统资源使用情况
3. 记录系统崩溃点

**监控指标**:
- CPU使用率
- 内存使用率
- 数据库连接数
- 响应时间

## 5. 安全测试案例

### 5.1 SQL注入测试

#### 测试案例 SEC001: 用户登录SQL注入
**测试目的**: 验证登录接口防SQL注入能力

**测试数据**:
```json
{
    "username": "admin' OR '1'='1",
    "password": "password"
}
```

**预期结果**:
- 登录失败
- 返回参数错误提示
- 不泄露数据库信息

### 5.2 XSS攻击测试

#### 测试案例 SEC002: 博客内容XSS测试
**测试目的**: 验证博客内容防XSS攻击

**测试数据**:
```html
<script>alert('XSS')</script>
```

**预期结果**:
- 脚本被过滤或转义
- 不执行恶意代码
- 正常显示文本内容

## 6. 兼容性测试案例

### 6.1 微信版本兼容性

#### 测试案例 COMP001: 不同微信版本测试
**测试目的**: 验证小程序在不同微信版本的兼容性

**测试版本**:
- 微信 8.0.x
- 微信 7.0.x
- 微信 6.7.x

**测试内容**:
- 基本功能正常
- 界面显示正确
- 性能表现稳定

### 6.2 设备兼容性

#### 测试案例 COMP002: 不同设备测试
**测试设备**:
- iPhone 12/13/14
- 华为P40/P50
- 小米11/12
- iPad

**测试内容**:
- 界面适配正确
- 触摸操作正常
- 性能表现良好

## 7. 测试执行计划

### 7.1 测试阶段
1. **单元测试**: 开发阶段同步进行
2. **集成测试**: 模块开发完成后
3. **系统测试**: 系统集成完成后
4. **验收测试**: 系统测试通过后

### 7.2 测试时间安排
- 功能测试: 3天
- 接口测试: 2天
- 性能测试: 2天
- 安全测试: 1天
- 兼容性测试: 1天

### 7.3 测试人员安排
- 测试经理: 1人
- 功能测试: 2人
- 自动化测试: 1人
- 性能测试: 1人

## 8. 缺陷管理

### 8.1 缺陷等级
- **严重**: 系统崩溃、数据丢失
- **高**: 主要功能无法使用
- **中**: 功能异常但有替代方案
- **低**: 界面问题、文字错误

### 8.2 缺陷处理流程
1. 发现缺陷 → 记录缺陷
2. 分析缺陷 → 分配开发
3. 修复缺陷 → 验证修复
4. 关闭缺陷 → 总结分析

## 9. 测试报告

### 9.1 测试总结
- 测试用例总数: 50个
- 通过用例数: 48个
- 失败用例数: 2个
- 通过率: 96%

### 9.2 质量评估
- 功能完整性: 95%
- 性能表现: 良好
- 安全性: 符合要求
- 用户体验: 优秀

### 9.3 建议
1. 优化博客加载性能
2. 增强错误提示信息
3. 完善异常处理机制
4. 加强安全防护措施

---

**注意事项**:
1. 测试用例需要定期更新
2. 自动化测试脚本需要维护
3. 性能基准需要定期评估
4. 安全测试需要持续进行
