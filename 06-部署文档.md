# 美聊平台部署文档

## 1. 部署概述

### 1.1 部署架构
真淘社交平台采用微服务架构，包含以下组件：
- Spring Boot后端服务
- MySQL数据库
- Redis缓存
- Nginx反向代理
- 微信小程序前端
- Vue管理后台

### 1.2 服务器要求

#### 最低配置
- **CPU**: 4核
- **内存**: 8GB
- **硬盘**: 100GB SSD
- **网络**: 10Mbps带宽
- **操作系统**: CentOS 7+ / Ubuntu 18.04+

#### 推荐配置
- **CPU**: 8核
- **内存**: 16GB
- **硬盘**: 200GB SSD
- **网络**: 100Mbps带宽
- **操作系统**: CentOS 8+ / Ubuntu 20.04+

## 2. 环境准备

### 2.1 基础软件安装

#### Java环境
```bash
# 安装OpenJDK 8
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 配置JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 验证安装
java -version
```

#### Maven安装
```bash
# 下载Maven
cd /opt
sudo wget https://archive.apache.org/dist/maven/maven-3/3.8.6/binaries/apache-maven-3.8.6-bin.tar.gz
sudo tar -xzf apache-maven-3.8.6-bin.tar.gz
sudo mv apache-maven-3.8.6 maven

# 配置环境变量
echo 'export MAVEN_HOME=/opt/maven' >> ~/.bashrc
echo 'export PATH=$MAVEN_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 验证安装
mvn -version
```

#### MySQL安装
```bash
# 安装MySQL 8.0
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p
CREATE DATABASE zhentao_socialize CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'zhentao'@'%' IDENTIFIED BY 'StrongPassword123!';
GRANT ALL PRIVILEGES ON zhentao_socialize.* TO 'zhentao'@'%';
FLUSH PRIVILEGES;
```

#### Redis安装
```bash
# 安装Redis
sudo yum install redis

# 配置Redis
sudo vim /etc/redis.conf
# 修改以下配置：
# bind 127.0.0.1 -> bind 0.0.0.0
# requirepass -> requirepass your_redis_password

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis

# 验证安装
redis-cli ping
```

#### Nginx安装
```bash
# 安装Nginx
sudo yum install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 验证安装
curl http://localhost
```

### 2.2 Node.js环境（管理后台）
```bash
# 安装Node.js 16+
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install nodejs

# 验证安装
node --version
npm --version
```

## 3. 数据库部署

### 3.1 数据库初始化

#### 创建数据库表
```sql
-- 用户表
CREATE TABLE tb_user (
    id VARCHAR(32) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码',
    phone VARCHAR(20) COMMENT '手机号',
    wx_id VARCHAR(50) COMMENT '微信OpenID',
    wx_username VARCHAR(50) COMMENT '微信昵称',
    head_pic VARCHAR(200) COMMENT '头像URL',
    role_id INT DEFAULT 1 COMMENT '角色ID',
    is_verified TINYINT DEFAULT 0 COMMENT '是否实名认证',
    is_member TINYINT DEFAULT 0 COMMENT '是否会员',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 交友圈表
CREATE TABLE tb_blog (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    images VARCHAR(500) COMMENT '图片URL',
    liked_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
);

-- 用户关注表
CREATE TABLE tb_user_follow (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    follower_id VARCHAR(32) NOT NULL COMMENT '关注者ID',
    following_id VARCHAR(32) NOT NULL COMMENT '被关注者ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_follow (follower_id, following_id)
);
```

#### 导入初始数据
```bash
# 导入SQL文件
mysql -u zhentao -p zhentao_socialize < sql/init_data.sql
```

### 3.2 数据库配置优化
```sql
-- MySQL配置优化
[mysqld]
max_connections = 1000
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
```

## 4. 后端服务部署

### 4.1 代码编译打包

#### 下载源码
```bash
# 克隆代码仓库
git clone https://github.com/your-repo/zhentao-socialize.git
cd zhentao-socialize
```

#### 配置文件修改
```bash
# 修改数据库配置
vim zhentao-service/zhentao-socialize-user1/src/main/resources/application.yml

spring:
  datasource:
    url: ***********************************************************************************************************
    username: zhentao
    password: StrongPassword123!
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
```

#### 编译打包
```bash
# 编译整个项目
mvn clean package -DskipTests

# 检查生成的jar包
ls -la */target/*.jar
```

### 4.2 服务部署

#### 创建部署目录
```bash
sudo mkdir -p /opt/zhentao-socialize
sudo mkdir -p /opt/zhentao-socialize/logs
sudo mkdir -p /opt/zhentao-socialize/config
```

#### 复制jar包
```bash
# 复制服务jar包
sudo cp zhentao-gateway/target/zhentao-gateway-1.0-SNAPSHOT.jar /opt/zhentao-socialize/
sudo cp zhentao-service/zhentao-socialize-user1/target/zhentao-socialize-user1-1.0-SNAPSHOT.jar /opt/zhentao-socialize/
sudo cp zhentao-service/soclialize-blog/target/soclialize-blog-1.0-SNAPSHOT.jar /opt/zhentao-socialize/
sudo cp zhentao-service/Poject_Ai_backend/target/Poject_Ai_backend-1.0-SNAPSHOT.jar /opt/zhentao-socialize/
sudo cp zhentao-manage/target/zhentao-manage-1.0-SNAPSHOT.jar /opt/zhentao-socialize/
```

#### 创建启动脚本
```bash
# 创建网关服务启动脚本
sudo vim /opt/zhentao-socialize/start-gateway.sh

#!/bin/bash
cd /opt/zhentao-socialize
nohup java -jar -Xms512m -Xmx1024m zhentao-gateway-1.0-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  > logs/gateway.log 2>&1 &
echo $! > gateway.pid

# 创建用户服务启动脚本
sudo vim /opt/zhentao-socialize/start-user-service.sh

#!/bin/bash
cd /opt/zhentao-socialize
nohup java -jar -Xms512m -Xmx1024m zhentao-socialize-user1-1.0-SNAPSHOT.jar \
  --spring.profiles.active=prod \
  --server.port=8081 \
  > logs/user-service.log 2>&1 &
echo $! > user-service.pid

# 设置执行权限
sudo chmod +x /opt/zhentao-socialize/*.sh
```

#### 创建系统服务
```bash
# 创建systemd服务文件
sudo vim /etc/systemd/system/zhentao-gateway.service

[Unit]
Description=Zhentao Gateway Service
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/opt/zhentao-socialize
ExecStart=/opt/zhentao-socialize/start-gateway.sh
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/zhentao-socialize/gateway.pid
Restart=always

[Install]
WantedBy=multi-user.target

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable zhentao-gateway
sudo systemctl start zhentao-gateway
```

### 4.3 AI服务部署

#### 安装Ollama
```bash
# 下载并安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 启动Ollama服务
sudo systemctl start ollama
sudo systemctl enable ollama

# 下载DeepSeek模型
ollama pull deepseek-r1:1.5b
```

#### 配置AI服务
```bash
# 修改AI服务配置
vim zhentao-service/Poject_Ai_backend/src/main/resources/application.properties

# Ollama配置
ollama.api.url=http://localhost:11434/api/chat
ollama.model=deepseek-r1:1.5b
ollama.temperature=0.7
ollama.max_tokens=1000
```

## 5. 前端部署

### 5.1 管理后台部署

#### 编译Vue项目
```bash
cd manage-vue

# 安装依赖
npm install

# 修改API地址
vim src/request/index.js
# 修改baseURL为生产环境地址

# 编译打包
npm run build
```

#### 部署到Nginx
```bash
# 复制构建文件
sudo cp -r dist/* /var/www/html/admin/

# 配置Nginx
sudo vim /etc/nginx/conf.d/zhentao-admin.conf

server {
    listen 80;
    server_name admin.zhentao.com;
    root /var/www/html/admin;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# 重启Nginx
sudo systemctl reload nginx
```

### 5.2 微信小程序部署

#### 配置小程序
```javascript
// 修改API配置
// web/utils/api.js
const BASE_URL = 'https://api.zhentao.com';

// 修改小程序配置
// web/project.config.json
{
  "appid": "your_miniprogram_appid",
  "projectname": "zhentao-socialize"
}
```

#### 上传小程序
1. 使用微信开发者工具打开web目录
2. 修改AppID为正式小程序ID
3. 点击"上传"按钮
4. 填写版本号和项目备注
5. 提交审核

## 6. Nginx配置

### 6.1 反向代理配置
```nginx
# 主配置文件
sudo vim /etc/nginx/nginx.conf

upstream zhentao_backend {
    server 127.0.0.1:8080 weight=1;
    server 127.0.0.1:8081 weight=1;
}

server {
    listen 80;
    server_name api.zhentao.com;
    
    # API代理
    location /api/ {
        proxy_pass http://zhentao_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 静态文件
    location /static/ {
        root /var/www/html;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

### 6.2 HTTPS配置
```bash
# 安装Certbot
sudo yum install certbot python3-certbot-nginx

# 申请SSL证书
sudo certbot --nginx -d api.zhentao.com -d admin.zhentao.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

## 7. 监控与日志

### 7.1 日志配置
```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/zhentao

/opt/zhentao-socialize/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload zhentao-gateway
    endscript
}
```

### 7.2 监控脚本
```bash
# 创建健康检查脚本
sudo vim /opt/zhentao-socialize/health-check.sh

#!/bin/bash
# 检查服务状态
services=("zhentao-gateway" "zhentao-user-service" "zhentao-blog-service")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is down, restarting..." >> /var/log/zhentao-health.log
        systemctl restart $service
    fi
done

# 检查数据库连接
mysql -u zhentao -p'StrongPassword123!' -e "SELECT 1" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "$(date): MySQL connection failed" >> /var/log/zhentao-health.log
fi

# 设置定时任务
sudo crontab -e
*/5 * * * * /opt/zhentao-socialize/health-check.sh
```

## 8. 备份策略

### 8.1 数据库备份
```bash
# 创建备份脚本
sudo vim /opt/zhentao-socialize/backup-db.sh

#!/bin/bash
BACKUP_DIR="/opt/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="zhentao_socialize_$DATE.sql"

mkdir -p $BACKUP_DIR

mysqldump -u zhentao -p'StrongPassword123!' \
  --single-transaction \
  --routines \
  --triggers \
  zhentao_socialize > $BACKUP_DIR/$BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_DIR/$BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

# 设置定时备份
sudo crontab -e
0 2 * * * /opt/zhentao-socialize/backup-db.sh
```

### 8.2 代码备份
```bash
# 创建代码备份脚本
sudo vim /opt/zhentao-socialize/backup-code.sh

#!/bin/bash
BACKUP_DIR="/opt/backups/code"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/zhentao-socialize_$DATE.tar.gz \
  /opt/zhentao-socialize \
  --exclude="*.log" \
  --exclude="*.pid"

# 删除30天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

## 9. 安全配置

### 9.1 防火墙配置
```bash
# 配置防火墙
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload

# 限制数据库访问
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='127.0.0.1' port protocol='tcp' port='3306' accept"
```

### 9.2 系统安全
```bash
# 禁用root远程登录
sudo vim /etc/ssh/sshd_config
PermitRootLogin no

# 创建专用用户
sudo useradd -m zhentao
sudo usermod -aG wheel zhentao

# 设置文件权限
sudo chown -R zhentao:zhentao /opt/zhentao-socialize
sudo chmod 750 /opt/zhentao-socialize
```

## 10. 部署验证

### 10.1 服务检查
```bash
# 检查服务状态
sudo systemctl status zhentao-gateway
sudo systemctl status mysql
sudo systemctl status redis
sudo systemctl status nginx

# 检查端口监听
netstat -tlnp | grep -E ':(80|443|3306|6379|8080|8081)'

# 检查日志
tail -f /opt/zhentao-socialize/logs/gateway.log
```

### 10.2 功能测试
```bash
# 测试API接口
curl -X GET http://localhost:8080/api/health

# 测试数据库连接
mysql -u zhentao -p'StrongPassword123!' -e "SELECT COUNT(*) FROM tb_user;"

# 测试Redis连接
redis-cli -a your_redis_password ping
```

## 11. 故障排除

### 11.1 常见问题

#### 服务启动失败
```bash
# 查看详细错误日志
journalctl -u zhentao-gateway -f

# 检查端口占用
lsof -i :8080

# 检查Java进程
ps aux | grep java
```

#### 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysqld

# 检查连接权限
mysql -u root -p
SHOW GRANTS FOR 'zhentao'@'%';

# 检查防火墙
sudo firewall-cmd --list-all
```

#### 内存不足
```bash
# 查看内存使用
free -h
top -p $(pgrep java)

# 调整JVM参数
vim /opt/zhentao-socialize/start-gateway.sh
# 修改 -Xms512m -Xmx1024m
```

### 11.2 性能优化
```bash
# JVM调优
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/opt/zhentao-socialize/logs/

# 数据库优化
# 增加连接池大小
spring.datasource.hikari.maximum-pool-size=20

# Redis优化
# 设置合适的内存策略
maxmemory-policy allkeys-lru
```

---

**部署完成检查清单**:
- [ ] 所有服务正常启动
- [ ] 数据库连接正常
- [ ] Redis缓存正常
- [ ] API接口可访问
- [ ] 小程序功能正常
- [ ] 管理后台可访问
- [ ] 日志记录正常
- [ ] 备份策略生效
- [ ] 监控告警配置
- [ ] 安全配置完成
