# 真淘社交平台接口文档

## 1. 接口概述

### 1.1 基本信息
- **基础URL**: `http://localhost:8080`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
    "code": 0,
    "msg": "操作成功",
    "data": {}
}
```

### 1.3 状态码说明
| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 失败 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 2. 用户服务接口

### 2.1 用户登录

**接口地址**: `POST /api/user/login`

**请求参数**:
```json
{
    "code": "微信登录code",
    "userInfo": {
        "nickName": "用户昵称",
        "avatarUrl": "头像URL",
        "gender": 1,
        "city": "城市",
        "province": "省份"
    }
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "userInfo": {
            "id": "user123",
            "username": "张三",
            "headPic": "https://example.com/avatar.jpg",
            "isVerified": false,
            "isMember": false
        }
    }
}
```

### 2.2 获取用户信息

**接口地址**: `GET /api/user/info/{userId}`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "id": "user123",
        "username": "张三",
        "phone": "13800138000",
        "headPic": "https://example.com/avatar.jpg",
        "isVerified": true,
        "isMember": false,
        "createTime": "2024-01-01 12:00:00"
    }
}
```

### 2.3 更新用户信息

**接口地址**: `PUT /api/user/update`

**请求参数**:
```json
{
    "userId": "user123",
    "username": "新昵称",
    "phone": "13800138000",
    "headPic": "https://example.com/new-avatar.jpg"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "更新成功",
    "data": null
}
```

### 2.4 实名认证

**接口地址**: `POST /rz/rz`

**请求参数**:
```json
{
    "name": "张三",
    "card": "110101199001011234",
    "userId": "user123"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "认证成功",
    "data": {
        "verified": true,
        "verifyTime": "2024-01-01 12:00:00",
        "realName": "张三"
    }
}
```

### 2.5 查询认证状态

**接口地址**: `GET /rz/status?userId={userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": {
        "isVerified": true,
        "verifyTime": "2024-01-01 12:00:00",
        "status": "已认证"
    }
}
```

## 3. 交友圈服务接口

### 3.1 获取交友圈列表

**接口地址**: `GET /blog/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10 |
| userId | string | 否 | 用户ID，获取指定用户博客 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "title": "交友圈标题",
                "content": "交友圈内容",
                "images": "https://example.com/image.jpg",
                "userId": "user123",
                "username": "张三",
                "userAvatar": "https://example.com/avatar.jpg",
                "likedCount": 15,
                "commentCount": 3,
                "createTime": "2024-01-01 12:00:00"
            }
        ]
    }
}
```

### 3.2 获取交友圈详情

**接口地址**: `GET /blog/{blogId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "title": "交友圈标题",
        "content": "交友圈内容详情...",
        "images": "https://example.com/image.jpg",
        "userId": "user123",
        "username": "张三",
        "userAvatar": "https://example.com/avatar.jpg",
        "likedCount": 15,
        "commentCount": 3,
        "isLiked": false,
        "createTime": "2024-01-01 12:00:00",
        "comments": [
            {
                "id": 1,
                "content": "评论内容",
                "userId": "user456",
                "username": "李四",
                "createTime": "2024-01-01 13:00:00"
            }
        ]
    }
}
```

### 3.3 发布交友圈

**接口地址**: `POST /blog/add`

**请求参数**:
```json
{
    "title": "交友圈标题",
    "content": "交友圈内容",
    "images": "https://example.com/image.jpg",
    "userId": "user123"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "发布成功",
    "data": {
        "blogId": 123
    }
}
```

### 3.4 交友圈点赞

**接口地址**: `GET /blog/likes/{blogId}?userId={userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "点赞成功",
    "data": {
        "isLiked": true,
        "likedCount": 16
    }
}
```

### 3.5 检查点赞状态

**接口地址**: `GET /blog/isLiked/{blogId}?userId={userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": {
        "isLiked": true,
        "likedCount": 15
    }
}
```

### 3.6 交友圈评论

**接口地址**: `POST /blog/comment`

**请求参数**:
```json
{
    "blogId": 1,
    "content": "评论内容",
    "userId": "user123"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "评论成功",
    "data": {
        "commentId": 456
    }
}
```

### 3.7 删除交友圈

**接口地址**: `DELETE /blog/{blogId}?userId={userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "删除成功",
    "data": null
}
```

## 4. 用户关注接口

### 4.1 关注用户

**接口地址**: `POST /user-follow/follow`

**请求参数**:
```json
{
    "followerId": "user123",
    "followingId": "user456"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "关注成功",
    "data": {
        "isFollowing": true
    }
}
```

### 4.2 取消关注

**接口地址**: `POST /user-follow/unfollow`

**请求参数**:
```json
{
    "followerId": "user123",
    "followingId": "user456"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "取消关注成功",
    "data": {
        "isFollowing": false
    }
}
```

### 4.3 获取关注列表

**接口地址**: `GET /user-follow/following/{userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": [
        {
            "userId": "user456",
            "username": "李四",
            "headPic": "https://example.com/avatar2.jpg",
            "followTime": "2024-01-01 12:00:00"
        }
    ]
}
```

## 5. AI客服接口

### 5.1 客服对话

**接口地址**: `POST /api/customer-service/chat`

**请求参数**:
```json
{
    "message": "我想了解会员服务",
    "userId": "user123"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "您好！我们的会员服务包括：1.无限制查看用户资料 2.优先推荐展示 3.专属客服服务。您想了解哪个方面呢？",
    "timestamp": 1640995200000
}
```

### 5.2 获取常见问题

**接口地址**: `GET /api/customer-service/faq`

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "question": "如何实名认证？",
            "answer": "进入个人中心，点击实名认证，填写真实姓名和身份证号即可。"
        },
        {
            "id": 2,
            "question": "会员有什么特权？",
            "answer": "会员可以无限制查看用户资料，享受优先推荐等特权。"
        }
    ]
}
```

## 6. 附近的人接口

### 6.1 获取附近用户

**接口地址**: `GET /nearby/users`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| latitude | double | 是 | 纬度 |
| longitude | double | 是 | 经度 |
| radius | int | 否 | 搜索半径(km)，默认10 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": [
        {
            "userId": "user456",
            "username": "李四",
            "headPic": "https://example.com/avatar2.jpg",
            "age": 25,
            "distance": 1.2,
            "isOnline": true,
            "lastActiveTime": "2024-01-01 12:00:00"
        }
    ]
}
```

## 7. 会员服务接口

### 7.1 检查会员状态

**接口地址**: `GET /member/status?userId={userId}`

**响应示例**:
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": {
        "isMember": true,
        "memberType": "VIP",
        "expireTime": "2024-12-31 23:59:59",
        "remainingDays": 365
    }
}
```

### 7.2 激活会员

**接口地址**: `POST /member/activate`

**请求参数**:
```json
{
    "userId": "user123",
    "memberType": "VIP",
    "duration": 365
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "激活成功",
    "data": {
        "expireTime": "2024-12-31 23:59:59"
    }
}
```

## 8. 错误码说明

### 8.1 用户相关错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 用户不存在 |
| 1002 | 用户已存在 |
| 1003 | 密码错误 |
| 1004 | 用户被禁用 |
| 1005 | 实名认证失败 |

### 8.2 博客相关错误码
| 错误码 | 说明 |
|--------|------|
| 2001 | 博客不存在 |
| 2002 | 无权限操作 |
| 2003 | 内容违规 |
| 2004 | 发布频率过快 |

### 8.3 系统相关错误码
| 错误码 | 说明 |
|--------|------|
| 9001 | 参数错误 |
| 9002 | 网络异常 |
| 9003 | 服务暂不可用 |
| 9999 | 系统错误 |

## 9. 接口调用示例

### 9.1 JavaScript示例
```javascript
// 用户登录
const login = async (code, userInfo) => {
    const response = await fetch('/api/user/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            code: code,
            userInfo: userInfo
        })
    });
    return await response.json();
};

// 发布博客
const postBlog = async (blogData, token) => {
    const response = await fetch('/blog/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(blogData)
    });
    return await response.json();
};
```

### 9.2 微信小程序示例
```javascript
// 调用博客列表接口
wx.request({
    url: 'http://localhost:8080/blog/list',
    method: 'GET',
    data: {
        page: 1,
        size: 10
    },
    success: (res) => {
        console.log('交友圈列表:', res.data);
    },
    fail: (err) => {
        console.error('请求失败:', err);
    }
});
```

## 10. 接口测试

### 10.1 测试环境
- **测试地址**: `http://localhost:8080`
- **测试工具**: Postman、Swagger UI
- **测试账号**: 联系管理员获取

### 10.2 接口文档更新
本文档会随着接口的更新而同步更新，请关注最新版本。

---

**注意事项**:
1. 所有接口都需要进行参数验证
2. 敏感操作需要用户认证
3. 请求频率有限制，避免恶意调用
4. 生产环境请使用HTTPS协议
