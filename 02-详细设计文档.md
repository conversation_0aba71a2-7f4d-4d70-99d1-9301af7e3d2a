# 真淘社交平台详细设计文档

## 1. 系统架构设计

### 1.1 总体架构
真淘社交平台采用微服务架构，包含以下核心组件：

```
┌─────────────────────────────────────────────────────────────┐
│                        客户端层                              │
├─────────────────────┬───────────────────────────────────────┤
│    微信小程序        │           Vue管理后台                  │
│  - 用户交互界面      │        - 后台管理界面                  │
│  - 社交功能         │        - 数据统计分析                  │
└─────────────────────┴───────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        网关层                                │
├─────────────────────────────────────────────────────────────┤
│              Spring Cloud Gateway                           │
│            - 路由转发  - 负载均衡  - 限流熔断                │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        服务层                                │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│  用户服务    │   博客服务   │  附近的人    │     AI客服服务       │
│- 用户管理    │- 博客发布    │- 地理位置    │  - 智能问答         │
│- 实名认证    │- 点赞评论    │- 用户匹配    │  - 对话管理         │
│- 会员体系    │- 内容审核    │- 推荐算法    │  - 知识库           │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        数据层                                │
├─────────────────────┬───────────────────────────────────────┤
│       MySQL         │              Redis                    │
│    - 业务数据        │           - 缓存数据                   │
│    - 用户信息        │           - 会话管理                   │
│    - 内容数据        │           - 计数器                     │
└─────────────────────┴───────────────────────────────────────┘
```

### 1.2 技术栈选型

#### 后端技术栈
- **Spring Boot 2.3.2**: 微服务框架
- **Spring Cloud Alibaba 2.2.5**: 微服务治理
- **MyBatis Plus**: ORM框架
- **MySQL 8.0**: 关系型数据库
- **Redis 6.0**: 缓存数据库
- **Ollama + DeepSeek-R1**: AI模型服务

#### 前端技术栈
- **微信小程序**: 原生开发
- **Vue 3**: 管理后台框架
- **Element Plus**: UI组件库
- **Vite**: 构建工具

## 2. 数据库设计

### 2.1 核心数据表

#### 用户表 (tb_user)
```sql
CREATE TABLE tb_user (
    id VARCHAR(32) PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码',
    phone VARCHAR(20) COMMENT '手机号',
    wx_id VARCHAR(50) COMMENT '微信OpenID',
    wx_username VARCHAR(50) COMMENT '微信昵称',
    head_pic VARCHAR(200) COMMENT '头像URL',
    role_id INT DEFAULT 1 COMMENT '角色ID',
    is_verified TINYINT DEFAULT 0 COMMENT '是否实名认证',
    is_member TINYINT DEFAULT 0 COMMENT '是否会员',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 交友圈表 (tb_blog)
```sql
CREATE TABLE tb_blog (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    images VARCHAR(500) COMMENT '图片URL',
    liked_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time)
);
```

#### 用户关注表 (tb_user_follow)
```sql
CREATE TABLE tb_user_follow (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    follower_id VARCHAR(32) NOT NULL COMMENT '关注者ID',
    following_id VARCHAR(32) NOT NULL COMMENT '被关注者ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_follow (follower_id, following_id)
);
```

#### 总数据库设计

```
-- TUser表
CREATE TABLE t_user (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    open_id VARCHAR(255) DEFAULT NULL COMMENT 'OpenID',
    session_key VARCHAR(255) DEFAULT NULL COMMENT '会话密钥',
    auth_time DATETIME DEFAULT NULL COMMENT '认证时间',
    state INT(11) DEFAULT NULL COMMENT '状态',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证信息表';

-- TbLike表
CREATE TABLE tb_like (
    blog_id BIGINT(20) NOT NULL COMMENT '博客ID',
    user_id VARCHAR(255) NOT NULL COMMENT '用户ID',
    PRIMARY KEY (blog_id, user_id) COMMENT '联合主键'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博客点赞表';

-- User表
CREATE TABLE user (
    user_id INT(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    id VARCHAR(255) DEFAULT NULL COMMENT '标识ID',
    wx_id VARCHAR(255) DEFAULT NULL COMMENT '微信ID',
    head_pic VARCHAR(255) DEFAULT NULL COMMENT '头像',
    wx_head_pic VARCHAR(255) DEFAULT NULL COMMENT '微信头像',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    role_id INT(11) DEFAULT NULL COMMENT '角色ID',
    sex INT(11) DEFAULT NULL COMMENT '性别(0-未知,1-男,2-女)',
    username VARCHAR(255) DEFAULT NULL COMMENT '用户名',
    wx_username VARCHAR(255) DEFAULT NULL COMMENT '微信昵称',
    code VARCHAR(255) DEFAULT NULL COMMENT '验证码',
    token VARCHAR(255) DEFAULT NULL COMMENT '令牌',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码',
    captcha VARCHAR(255) DEFAULT NULL COMMENT '验证码',
    correct_captcha VARCHAR(255) DEFAULT NULL COMMENT '正确验证码',
    x DOUBLE DEFAULT NULL COMMENT '经度',
    y DOUBLE DEFAULT NULL COMMENT '纬度',
    PRIMARY KEY (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- TbBlog表
CREATE TABLE tb_blog (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '博客ID',
    user_id VARCHAR(255) DEFAULT NULL COMMENT '用户ID',
    title VARCHAR(255) DEFAULT NULL COMMENT '标题',
    images VARCHAR(1000) DEFAULT NULL COMMENT '图片集合(逗号分隔)',
    content TEXT DEFAULT NULL COMMENT '内容',
    liked INT(11) DEFAULT 0 COMMENT '点赞数',
    comments INT(11) DEFAULT 0 COMMENT '评论数',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博客表';

-- TbSign表
CREATE TABLE tb_sign (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '签到ID',
    user_id BIGINT(20) DEFAULT NULL COMMENT '用户ID',
    year INT(4) DEFAULT NULL COMMENT '年份',
    month INT(2) DEFAULT NULL COMMENT '月份',
    date DATETIME DEFAULT NULL COMMENT '签到日期',
    is_backup INT(1) DEFAULT 0 COMMENT '是否备份(0-否,1-是)',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签到表';

-- Announcement表
CREATE TABLE announcement (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    content TEXT DEFAULT NULL COMMENT '公告内容',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- Orders表
CREATE TABLE orders (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    trace_no VARCHAR(255) DEFAULT NULL COMMENT '跟踪号',
    order_no VARCHAR(255) DEFAULT NULL COMMENT '订单号',
    user_id VARCHAR(255) DEFAULT NULL COMMENT '用户ID',
    status INT(11) DEFAULT NULL COMMENT '状态(0-待支付,1-已支付,2-取消)',
    pay_type VARCHAR(20) DEFAULT NULL COMMENT '支付方式',
    description VARCHAR(500) DEFAULT NULL COMMENT '描述',
    transaction_id VARCHAR(255) DEFAULT NULL COMMENT '交易ID',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    pay_time DATETIME DEFAULT NULL COMMENT '支付时间',
    total_amount VARCHAR(50) DEFAULT NULL COMMENT '总金额(字符串)',
    amount DOUBLE DEFAULT NULL COMMENT '金额(数值)',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- TbCarouselChart表
CREATE TABLE tb_carousel_chart (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
    topic VARCHAR(255) DEFAULT NULL COMMENT '主题',
    title VARCHAR(255) DEFAULT NULL COMMENT '标题',
    image VARCHAR(255) DEFAULT NULL COMMENT '图片路径',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- ManageUser表
CREATE TABLE manage_user (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(255) DEFAULT NULL COMMENT '用户名',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码',
    age INT(3) DEFAULT NULL COMMENT '年龄',
    sex INT(1) DEFAULT NULL COMMENT '性别(0-未知,1-男,2-女)',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    photo VARCHAR(255) DEFAULT NULL COMMENT '照片',
    emill VARCHAR(255) DEFAULT NULL COMMENT '邮箱(注:字段名可能为email拼写错误)',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- TbActivity表
CREATE TABLE tb_activity (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    name VARCHAR(255) DEFAULT NULL COMMENT '活动名称',
    type VARCHAR(50) DEFAULT NULL COMMENT '活动类型',
    participate INT(11) DEFAULT 0 COMMENT '参与人数',
    start INT(1) DEFAULT 0 COMMENT '是否开始(0-未开始,1-已开始)',
    img VARCHAR(255) DEFAULT NULL COMMENT '活动图片',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- UserBalance表
CREATE TABLE user_balance (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '余额ID',
    user_id VARCHAR(255) DEFAULT NULL COMMENT '用户ID',
    available_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '可用余额',
    frozen_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '冻结余额',
    total_income DECIMAL(10,2) DEFAULT 0.00 COMMENT '总收入',
    total_withdrawal DECIMAL(10,2) DEFAULT 0.00 COMMENT '总提现',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额表';

-- Matchmaker表
CREATE TABLE matchmaker (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '红娘ID',
    user_id VARCHAR(255) DEFAULT NULL COMMENT '用户ID',
    images VARCHAR(1000) DEFAULT NULL COMMENT '图片集合(逗号分隔)',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    status INT(1) DEFAULT 0 COMMENT '状态(0-待审核,1-已认证,2-已拒绝)',
    certification_time DATETIME DEFAULT NULL COMMENT '认证时间',
    promotion_count INT(11) DEFAULT 0 COMMENT '推广次数',
    total_income DECIMAL(10,2) DEFAULT 0.00 COMMENT '总收入',
    available_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '可用余额',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='红娘表';

-- Minio表
CREATE TABLE minio (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
    path VARCHAR(255) DEFAULT NULL COMMENT '文件路径',
    title VARCHAR(255) DEFAULT NULL COMMENT '文件标题',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MinIO文件存储表';

-- NearbyType表
CREATE TABLE nearby_type (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '附近类型ID',
    name VARCHAR(255) DEFAULT NULL COMMENT '类型名称',
    sort INT(11) DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附近类型表';

-- WithdrawalRecord表
CREATE TABLE withdrawal_record (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
    user_id VARCHAR(255) DEFAULT NULL COMMENT '用户ID',
    amount DECIMAL(10,2) DEFAULT NULL COMMENT '提现金额',
    bank_card VARCHAR(50) DEFAULT NULL COMMENT '银行卡号',
    bank_name VARCHAR(255) DEFAULT NULL COMMENT '银行名称',
    real_name VARCHAR(255) DEFAULT NULL COMMENT '真实姓名',
    status INT(1) DEFAULT 0 COMMENT '状态(0-申请中,1-已处理,2-已完成,3-已拒绝)',
    apply_time DATETIME DEFAULT NULL COMMENT '申请时间',
    process_time DATETIME DEFAULT NULL COMMENT '处理时间',
    complete_time DATETIME DEFAULT NULL COMMENT '完成时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

-- Nearby表
CREATE TABLE nearby (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '附近信息ID',
    user_name VARCHAR(255) DEFAULT NULL COMMENT '用户名',
    type_id INT(11) DEFAULT NULL COMMENT '类型ID(关联nearby_type表)',
    images VARCHAR(1000) DEFAULT NULL COMMENT '图片集合(逗号分隔)',
    area VARCHAR(255) DEFAULT NULL COMMENT '区域',
    address VARCHAR(500) DEFAULT NULL COMMENT '详细地址',
    x DOUBLE DEFAULT NULL COMMENT '经度',
    y DOUBLE DEFAULT NULL COMMENT '纬度',
    PRIMARY KEY (id),
    KEY idx_type (type_id) COMMENT '类型索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附近信息表';
```



### 2.2 Redis数据结构设计

#### 点赞数据
```
Key: blog:liked:{blogId}
Type: ZSet
Value: userId -> timestamp
```

#### 用户会话
```
Key: user:session:{userId}
Type: Hash
Fields: token, loginTime, lastActiveTime
```

#### 缓存数据
```
Key: cache:blog:{blogId}
Type: String
Value: JSON格式的博客详情
TTL: 3600秒
```

## 3. 接口设计

### 3.1 用户服务接口

#### 用户登录
```http
POST /api/user/login
Content-Type: application/json

{
    "code": "微信登录code",
    "userInfo": {
        "nickName": "用户昵称",
        "avatarUrl": "头像URL"
    }
}

Response:
{
    "code": 0,
    "msg": "登录成功",
    "data": {
        "token": "jwt_token",
        "userInfo": {
            "id": "user_id",
            "username": "用户名",
            "headPic": "头像URL"
        }
    }
}
```

#### 实名认证
```http
POST /api/user/verify
Content-Type: application/json

{
    "name": "真实姓名",
    "idCard": "身份证号",
    "userId": "用户ID"
}

Response:
{
    "code": 0,
    "msg": "认证成功",
    "data": {
        "verified": true,
        "verifyTime": "2024-01-01 12:00:00"
    }
}
```

### 3.2 交友圈服务接口

#### 发布交友圈
```http
POST /api/blog/add
Content-Type: application/json

{
    "title": "交友圈标题",
    "content": "交友圈内容",
    "images": "图片URL",
    "userId": "用户ID"
}

Response:
{
    "code": 0,
    "msg": "发布成功",
    "data": {
        "blogId": 123
    }
}
```

#### 交友圈点赞
```http
GET /api/blog/likes/{blogId}?userId={userId}

Response:
{
    "code": 0,
    "msg": "点赞成功",
    "data": {
        "isLiked": true,
        "likedCount": 15
    }
}
```

### 3.3 AI客服接口

#### 客服对话
```http
POST /api/customer-service/chat
Content-Type: application/json

{
    "message": "用户消息",
    "userId": "用户ID"
}

Response:
{
    "success": true,
    "message": "AI回复内容",
    "timestamp": 1640995200000
}
```

## 4. 核心业务逻辑设计

### 4.1 用户认证流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant GW as 网关
    participant US as 用户服务
    participant WX as 微信服务器
    
    U->>MP: 点击登录
    MP->>WX: wx.login()获取code
    WX-->>MP: 返回code
    MP->>GW: 发送登录请求
    GW->>US: 转发请求
    US->>WX: code换取openid
    WX-->>US: 返回用户信息
    US->>US: 生成JWT token
    US-->>GW: 返回登录结果
    GW-->>MP: 返回token
    MP->>MP: 存储token
```

### 4.2 交友圈点赞逻辑
```java
@Service
public class BlogService {
    
    public Result likeBlog(Long blogId, String userId) {
        // 1. 检查Redis中的点赞状态
        String key = "blog:liked:" + blogId;
        Boolean isLiked = redisTemplate.opsForZSet()
            .score(key, userId) != null;
        
        if (isLiked) {
            // 取消点赞
            redisTemplate.opsForZSet().remove(key, userId);
            blogMapper.decrementLikeCount(blogId);
        } else {
            // 添加点赞
            redisTemplate.opsForZSet()
                .add(key, userId, System.currentTimeMillis());
            blogMapper.incrementLikeCount(blogId);
        }
        
        // 返回最新状态
        Long likedCount = redisTemplate.opsForZSet().count(key, 0, -1);
        return Result.success(Map.of(
            "isLiked", !isLiked,
            "likedCount", likedCount
        ));
    }
}
```

### 4.3 AI客服处理流程
```java
@RestController
public class ChatController {
    
    @PostMapping("/chat")
    public ResponseEntity<Map<String, Object>> chat(
            @RequestBody ChatRequest request) {
        
        try {
            // 1. 构建客服上下文
            String context = buildCustomerServiceContext();
            
            // 2. 调用AI模型
            OllamaRequest ollamaRequest = new OllamaRequest();
            ollamaRequest.setModel("deepseek-r1:1.5b");
            ollamaRequest.setPrompt(context + "\n用户问题: " + request.getMessage());
            
            // 3. 获取AI回复
            OllamaResponse response = ollamaClient.chat(ollamaRequest);
            
            // 4. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", response.getResponse());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            // 错误处理
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "抱歉，我暂时无法回答您的问题，请联系人工客服。"
            ));
        }
    }
}
```

## 5. 安全设计

### 5.1 认证授权
- **JWT Token**: 用户身份认证
- **角色权限**: 基于RBAC的权限控制
- **接口鉴权**: 网关层统一鉴权

### 5.2 数据安全
- **敏感数据加密**: 身份证号、手机号等
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输入输出过滤

### 5.3 接口安全
- **限流控制**: 防止接口被恶意调用
- **参数校验**: 严格的参数验证
- **HTTPS**: 全站HTTPS加密传输

## 6. 性能优化设计

### 6.1 缓存策略
- **Redis缓存**: 热点数据缓存
- **本地缓存**: 配置数据缓存
- **CDN**: 静态资源加速

### 6.2 数据库优化
- **索引优化**: 合理创建数据库索引
- **分页查询**: 避免大数据量查询
- **读写分离**: 主从数据库分离

### 6.3 代码优化
- **异步处理**: 耗时操作异步化
- **连接池**: 数据库连接池优化
- **JVM调优**: 内存和GC优化

## 7. 监控告警设计

### 7.1 系统监控
- **应用监控**: Spring Boot Actuator
- **性能监控**: JVM性能指标
- **业务监控**: 关键业务指标

### 7.2 日志管理
- **日志收集**: ELK技术栈
- **日志分级**: ERROR/WARN/INFO/DEBUG
- **日志轮转**: 定期清理历史日志

### 7.3 告警机制
- **阈值告警**: 系统指标超阈值告警
- **异常告警**: 系统异常实时告警
- **业务告警**: 业务异常告警
